﻿import React from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import parse from "html-react-parser";
import WHOTabPanel from "./WHOTabPanel";
import { TabModel } from "../../models/TabModel";
import classes from "./WHOTabs.module.css";
import classNames from "classnames";

function a11yProps(index: any) {
  return {
    id: `scrollable-auto-tab-${index}`,
    "aria-controls": `scrollable-auto-tabpanel-${index}`,
  };
}

type WHOTabs = {
  tabs: Array<TabModel>;
  scrollable: true | false | undefined;
  defaultdSeletedTabIndex?: number;
  value?: any;
  children?: React.ReactElement;
  onChange?: (event: React.ChangeEvent<{}>, newValue: any) => void;
  orientation?: "vertical" | "horizontal" | undefined;
  centered?: boolean;
};

/**
 * Renders the Tab control
 * @param props: an object of type WHOTabs
 */
const WHOTabs = (props: WHOTabs) => {
  const {
    tabs,
    children,
    value,
    scrollable,
    orientation,
    centered,
    defaultdSeletedTabIndex,
    onChange,
    ...otherProps
  } = props;
  const tabIndex = tabs?.findIndex((tab: TabModel) => tab?.id === value) || 0;

  const Children = (): any =>
    tabs ? tabs.length > 0 ? tabs[tabIndex].children : <></> : <></>;

  return (
    <div className={classNames("row", classes.container)}>
      <div
        className={
          orientation === "vertical"
            ? `${classNames("tabs-wrapper col-sm-3", classes.tabsWrapper)}`
            : `${classNames("tabs-wrapper mt-3", classes.tabsWrapper)}`
        }
      >
        <Tabs
          indicatorColor='primary'
          textColor='primary'
          orientation={orientation ? "vertical" : "horizontal"}
          variant={scrollable ? "scrollable" : "fullWidth"}
          scrollButtons={scrollable ? "auto" : "off"}
          aria-label={`scrollable ${scrollable && "auto"} tabs`}
          className={scrollable ? "app-tabs app-tabs-scrollable" : "app-tabs"}
          centered={centered}
          value={value}
          onChange={onChange}
          {...otherProps}
        >
          {tabs?.map((tab: TabModel, index: number) => (
            <Tab
              className={"app-tab"}
              key={`${tab.id}_${index}`}
              label={parse(tab.label)}
              {...a11yProps(index)}
              icon={tab.icon}
            />
          ))}
        </Tabs>
      </div>

      <div className={orientation === "vertical" ? "col-sm-9" : ""}>
        {!children ? (
          <WHOTabPanel value={tabIndex} index={tabIndex}>
            <Children key={Math.random()} />
          </WHOTabPanel>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

export default WHOTabs;
